import 'dart:convert';
import 'package:flutter_voice_alarm/utils/alarm_helper.dart';

class Model {
  final int id;
  final String label;
  final String dateTime;
  late int checkInt;
  final String when;
  final int milliseconds;
  late int aiAlarmInt;
  final String? weatherType;
  final Map<String, dynamic>? weatherData;
  final List<bool> repeatDays;
  final String alarmSound;
  final double volume;

  // Getter 및 Setter: bool <-> int 변환 처리
  bool get check => checkInt == 1;
  set check(bool value) => checkInt = value ? 1 : 0;

  bool get aiAlarm => aiAlarmInt == 1;
  set aiAlarm(bool value) => aiAlarmInt = value ? 1 : 0;

  Model({
    required this.id,
    required this.label,
    required this.dateTime,
    required bool check,
    required this.when,
    required this.milliseconds,
    bool aiAlarm = false,
    this.weatherData,
    this.weatherType,
    List<bool>? repeatDays,
    this.volume = 1.0,
    String? alarmSound,
  })  : checkInt = check ? 1 : 0,
        aiAlarmInt = aiAlarm ? 1 : 0,
        repeatDays = repeatDays ?? List<bool>.filled(7, false),
        alarmSound = alarmSound ?? AlarmHelper.defaultAlarmSound;

  // 데이터베이스에서 불러온 데이터를 모델로 변환
  factory Model.fromJson(Map<String, dynamic> json) {
    return Model(
      id: json['id'],
      label: json['label'],
      dateTime: json['dateTime'],
      check: json['check'] == 1,
      when: json['when'],
      milliseconds: json['milliseconds'],
      aiAlarm: json['aiAlarm'] == 1,
      weatherData: json['weatherData'] != null
          ? Map<String, dynamic>.from(jsonDecode(json['weatherData']))
          : null,
      weatherType: json['weatherType'],
      repeatDays: json['repeatDays'] != null
          ? List<bool>.from(jsonDecode(json['repeatDays']))
          : List<bool>.filled(7, false),
      alarmSound: json['alarmSound'] ?? AlarmHelper.defaultAlarmSound,
      volume: json['volume'] ?? 1.0,
    );
  }

  // 모델 데이터를 데이터베이스에 저장할 JSON 형태로 변환
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'label': label,
      'dateTime': dateTime,
      'check': check ? 1 : 0,
      'when': when,
      'milliseconds': milliseconds,
      'aiAlarm': aiAlarm ? 1 : 0,
      'weatherData': weatherData != null ? jsonEncode(weatherData) : null,
      'weatherType': weatherType,
      'repeatDays': jsonEncode(repeatDays),
      'alarmSound': alarmSound,
      'volume': volume,
    };
  }

  // 모델을 복사하는 메서드 (부분적으로 업데이트 가능)
  Model copyWith({
    int? id,
    String? label,
    String? dateTime,
    bool? check,
    String? when,
    int? milliseconds,
    bool? aiAlarm,
    String? weatherType,
    Map<String, dynamic>? weatherData,
    List<bool>? repeatDays,
    String? alarmSound,
    double? volume,
  }) {
    return Model(
      id: id ?? this.id,
      label: label ?? this.label,
      dateTime: dateTime ?? this.dateTime,
      check: check ?? this.check,
      when: when ?? this.when,
      milliseconds: milliseconds ?? this.milliseconds,
      aiAlarm: aiAlarm ?? this.aiAlarm,
      weatherType: weatherType ?? this.weatherType,
      weatherData: weatherData ?? this.weatherData,
      repeatDays: repeatDays ?? List<bool>.from(this.repeatDays),
      alarmSound: alarmSound ?? this.alarmSound,
      volume: volume ?? this.volume,
    );
  }
}
