plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.example.flutter_voice_alarm"
    compileSdkVersion 34
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId = "com.example.flutter_voice_alarm"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 2  
        versionName "1.0.2" 

        // 프로가드 설정 파일
        proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

        // Application icon
        resValue "mipmap", "ic_launcher", "@mipmap/ic_launcher"
    }

    signingConfigs {
    release {
        keyAlias 'han'
        keyPassword 'kuh1594ss'
        storeFile file('C:/Users/<USER>/Documents/code/flutter_voice_alarm/release-key.jks')
        storePassword 'kuh1594ss'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }

}

flutter {
    source = "../.."
}

dependencies {
    implementation 'com.google.android.material:material:1.9.0'
}
