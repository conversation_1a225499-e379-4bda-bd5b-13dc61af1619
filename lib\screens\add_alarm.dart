import 'dart:math';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_voice_alarm/models/model.dart';
import 'package:flutter_voice_alarm/providers/alarm_provider.dart';
import 'package:flutter_voice_alarm/screens/home_screen.dart';
import 'package:flutter_voice_alarm/utils/alarm_helper.dart';
import 'package:flutter_voice_alarm/utils/permission_handler.dart';
import 'package:flutter_voice_alarm/services/weather_service.dart';
import 'package:flutter_voice_alarm/services/database_service.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:path/path.dart' as path_util;
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';

class AddAlarm extends StatefulWidget {
  final Model? alarm;

  const AddAlarm({super.key, this.alarm});

  @override
  State<AddAlarm> createState() => _AddAlarmState();
}

class _AddAlarmState extends State<AddAlarm> {
  // --------------------------------------
  // Fields & Constants
  // --------------------------------------
  static const List<String> _days = ['일', '월', '화', '수', '목', '금', '토'];
  static final Logger _logger = Logger();

  final PermissionHandler _permissionHandler = PermissionHandler();
  final DatabaseService _databaseService = DatabaseService();

  late TextEditingController controller;
  late DateTime notificationTime;

  int repeatDays = 0;
  bool aiAlarm = false;
  bool isLoading = false;
  String selectedSound = AlarmHelper.defaultAlarmSound;
  double alarmVolume = 0.8;

  // --------------------------------------
  // Lifecycle
  // --------------------------------------
  @override
  void initState() {
    super.initState();
    _logger.i('========== [AddAlarm] initState 시작 ==========');
    _initializeDatabase();
    _initializeValues();
    _logger.i('========== [AddAlarm] 초기화 완료 ==========');
  }

  @override
  void dispose() {
    _logger.d('========== [AddAlarm] dispose 호출 ==========');
    controller.dispose();
    super.dispose();
  }

  Future<void> _initializeDatabase() async {
    try {
      _logger.i('[DB] 데이터베이스 초기화 시작');
      await _databaseService.database; // 실제로 DB 오픈
      _logger.i('[DB] 데이터베이스 초기화 완료');
    } catch (e, stackTrace) {
      _logger.e('[DB] 데이터베이스 초기화 실패', error: e, stackTrace: stackTrace);
    }
  }

  /// 위젯에 넘겨받은 `widget.alarm`으로부터 초기값 설정
  void _initializeValues() {
    _logger.d('[Init] _initializeValues 호출');

    // 라벨
    controller = TextEditingController(text: widget.alarm?.label ?? '');

    // 알람 시간
    notificationTime = widget.alarm != null
        ? DateTime.fromMillisecondsSinceEpoch(widget.alarm!.milliseconds)
        : DateTime.now().add(const Duration(minutes: 1));

    // AI 알람 여부
    aiAlarm = widget.alarm?.aiAlarm ?? false;

    // 알람 볼륨
    alarmVolume = widget.alarm?.volume ?? 0.8;

    // 알람 사운드
    selectedSound = widget.alarm?.alarmSound ?? AlarmHelper.defaultAlarmSound;

    // 반복 요일
    if (widget.alarm != null) {
      final days = widget.alarm!.when.split(', ');
      for (int i = 0; i < _days.length; i++) {
        if (days.contains(_days[i])) {
          repeatDays |= (1 << i);
        }
      }
    }

    // 로그 출력
    _logger.i('[Init] 라벨: ${controller.text}');
    _logger.i('[Init] 알람 시간: $notificationTime');
    _logger.i('[Init] AI 알람: $aiAlarm');
    _logger.i('[Init] 볼륨: $alarmVolume');
    _logger.i('[Init] 사운드: $selectedSound');
    _logger.i('[Init] repeatDays: $repeatDays');
  }

  // --------------------------------------
  // UI Builders
  // --------------------------------------
  @override
  Widget build(BuildContext context) {
    _logger.d('========== [AddAlarm] 화면 빌드 시작 ==========');

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.alarm != null ? '알람 수정' : '알람 추가',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          // 뒤로가기: 그냥 이전 화면 복귀 (저장 X)
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: isLoading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.0,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.check),
            onPressed: isLoading
                ? null
                : () async {
                    _logger.d('[체크 버튼] 클릭됨 -> 권한 체크');

                    // 새로운 PermissionHandler API 사용
                    final permissionResults =
                        await _permissionHandler.requestAllPermissions();
                    final alarmPermissionGranted =
                        permissionResults[Permission.scheduleExactAlarm] ??
                            false;

                    if (!alarmPermissionGranted) {
                      if (!mounted) return;
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content:
                              Text('알람 설정을 위해서는 권한이 필요합니다. 설정에서 권한을 허용해주세요.'),
                          duration: Duration(seconds: 3),
                        ),
                      );
                      return;
                    }

                    await _saveAlarm();

                    _logger.d('[체크 버튼] _saveAlarm() 완료 후 -> 홈 이동');
                    if (!mounted) return;
                    Navigator.of(context).pushAndRemoveUntil(
                      MaterialPageRoute(builder: (_) => const HomeScreen()),
                      (route) => false,
                    );
                    _logger.d('[체크 버튼] 화면 전환 완료');
                  },
          ),
        ],
      ),
      body: Theme(
        data: Theme.of(context).copyWith(
          sliderTheme: const SliderThemeData(
            showValueIndicator: ShowValueIndicator.always,
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTimePicker(),
                const SizedBox(height: 16),
                _buildDaySelector(),
                const SizedBox(height: 16),
                _buildAlarmControls(),
                const SizedBox(height: 16),
                _buildLabelInput(),
                const SizedBox(height: 16),
                Text(
                  '알림 유형: ${aiAlarm ? "AI 알람" : "기본 알람"}',
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                _buildSoundSelector(),
                const SizedBox(height: 16),
                _buildVolumeControl(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTimePicker() {
    return SizedBox(
      height: 180,
      child: CupertinoDatePicker(
        mode: CupertinoDatePickerMode.time,
        use24hFormat: false,
        initialDateTime: notificationTime,
        onDateTimeChanged: (DateTime newDateTime) {
          _logger.d('[_buildTimePicker] 알람 시간 변경: $newDateTime');
          setState(() {
            notificationTime = newDateTime;
          });
        },
      ),
    );
  }

  Widget _buildDaySelector() {
    _logger.d('[_buildDaySelector] 요일 선택 위젯 빌드');
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(
        7,
        (index) => _buildDayChip(_days[index], index),
      ),
    );
  }

  Widget _buildDayChip(String day, int index) {
    final isSelected = (repeatDays & (1 << index)) != 0;

    // 일/토 색상 처리
    Color textColor;
    if (day == '일') {
      textColor = Colors.red;
    } else if (day == '토') {
      textColor = Colors.blue;
    } else {
      textColor = isSelected ? Colors.white : Colors.black;
    }

    return GestureDetector(
      onTap: () {
        _logger.d('[_buildDayChip] "$day" 칩 탭됨. isSelected=$isSelected');
        setState(() {
          if (isSelected) {
            repeatDays &= ~(1 << index);
          } else {
            repeatDays |= (1 << index);
          }
          _logger.d('[_buildDayChip] repeatDays 갱신: $repeatDays');
        });
      },
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isSelected ? Theme.of(context).primaryColor : Colors.grey[200],
        ),
        child: Center(
          child: Text(
            day,
            style: TextStyle(
              fontSize: 14,
              color: textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAlarmControls() {
    _logger.d('[_buildAlarmControls] "매일" 버튼 + AI 알람 스위치 빌드');
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // "매일" 버튼 (요일 전체 선택)
        ElevatedButton(
          onPressed: () {
            _logger.d('[_buildAlarmControls] "매일" 버튼 탭');
            setState(() {
              repeatDays = (1 << 7) - 1; // 0b1111111 (일~토 7일 모두 선택)
            });
          },
          child: const Text('매일'),
        ),
        // AI 알람 스위치
        Row(
          children: [
            const Text('AI 알람'),
            CupertinoSwitch(
              value: aiAlarm,
              onChanged: (bool value) {
                _logger.d('[_buildAlarmControls] AI 스위치 -> $value');
                setState(() {
                  aiAlarm = value;
                });
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLabelInput() {
    _logger.d('[_buildLabelInput] 알람 라벨 입력 필드 빌드');
    return TextField(
      controller: controller,
      decoration: const InputDecoration(
        labelText: '알람 이름',
        border: OutlineInputBorder(),
      ),
      onChanged: (value) {
        _logger.d('[_buildLabelInput] 라벨 입력: $value');
      },
    );
  }

  Widget _buildSoundSelector() {
    _logger.d('[_buildSoundSelector] 사운드 선택 드롭다운 빌드');

    // AI 알람이면 사운드 선택 숨김
    if (aiAlarm) {
      return const SizedBox.shrink();
    }

    // 커스텀 사운드 여부 확인
    bool isCustomSound = !AlarmHelper.sounds.contains(selectedSound);
    List<DropdownMenuItem<String>> items = AlarmHelper.sounds
        .map((String sound) => DropdownMenuItem<String>(
              value: sound,
              child: Text(_formatSoundName(sound)),
            ))
        .toList();

    // 만약 커스텀 사운드가 현재 선택되어 있다면, DropdownItem에 추가
    if (isCustomSound && selectedSound.isNotEmpty) {
      items.add(
        DropdownMenuItem<String>(
          value: selectedSound,
          child: Text(path_util.basename(selectedSound)),
        ),
      );
    }

    // 현재 드롭다운 값
    String currentValue = selectedSound;
    // 드롭다운에 없는 값이면 기본사운드로
    if (!items.any((item) => item.value == currentValue)) {
      currentValue = AlarmHelper.defaultAlarmSound;
      _logger.w(
        '[_buildSoundSelector] "$selectedSound"이 목록에 없음 -> 기본 사운드로 변경',
      );
    }

    return Row(
      children: [
        const Text(
          '알람 사운드:',
          style: TextStyle(fontSize: 16),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: DropdownButton<String>(
            value: currentValue,
            isExpanded: true,
            onChanged: (String? newValue) {
              if (newValue != null) {
                _logger.d('[_buildSoundSelector] 사운드 선택: $newValue');
                setState(() {
                  selectedSound = newValue;
                });
              }
            },
            items: items,
          ),
        ),
        IconButton(
          icon: const Icon(Icons.folder_open),
          onPressed: _pickFile,
        ),
      ],
    );
  }

  Widget _buildVolumeControl() {
    _logger.d('[_buildVolumeControl] 볼륨 슬라이더 빌드');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('볼륨:', style: TextStyle(fontSize: 16)),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              const Icon(Icons.volume_down),
              Expanded(
                child: Slider(
                  value: alarmVolume,
                  onChanged: (value) {
                    _logger.d('[_buildVolumeControl] 볼륨 변경: $value');
                    setState(() {
                      alarmVolume = value;
                    });
                  },
                  min: 0.0,
                  max: 1.0,
                  divisions: 10,
                  label: '${(alarmVolume * 100).round()}%',
                  activeColor: Theme.of(context).primaryColor,
                  inactiveColor: Colors.grey[400],
                ),
              ),
              const Icon(Icons.volume_up),
            ],
          ),
        ),
      ],
    );
  }

  // --------------------------------------
  // File picking
  // --------------------------------------
  Future<void> _pickFile() async {
    _logger.d('[_pickFile] 오디오 파일 선택 시도');
    try {
      final result = await FilePicker.platform.pickFiles(type: FileType.audio);
      if (result != null && result.files.single.path != null) {
        final path = result.files.single.path!;
        setState(() {
          selectedSound = path;
        });
        _logger.i('[_pickFile] 사용자 정의 사운드 선택됨: $selectedSound');
      } else {
        _logger.w('[_pickFile] 파일 선택 취소 or 경로 null');
      }
    } catch (e, st) {
      _logger.e('[_pickFile] 파일 선택 오류', error: e, stackTrace: st);
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('파일 선택 오류: $e')),
      );
    }
  }

  // --------------------------------------
  // Alarm Save
  // --------------------------------------
  Future<void> _saveAlarm() async {
    _logger.i('[_saveAlarm] 시작');
    setState(() => isLoading = true);

    try {
      final alarmProvider = Provider.of<AlarmProvider>(context, listen: false);
      final newAlarm = await _prepareAlarmData();
      _logger.d('[_saveAlarm] 준비된 알람: ${newAlarm.id}');

      if (widget.alarm != null) {
        _logger.d('[_saveAlarm] 기존 알람 수정 시도');
        final oldId = widget.alarm!.id;
        // 만약 updateAlarmById가 있다면:
        await alarmProvider.updateAlarmById(oldId, newAlarm);
        _logger.i('[_saveAlarm] 알람 수정 완료, oldId=$oldId');
      } else {
        _logger.d('[_saveAlarm] 신규 알람 등록 시도');
        await alarmProvider.setAlarm(newAlarm);
        _logger.i('[_saveAlarm] 신규 알람 등록 완료, newId=${newAlarm.id}');
      }
    } catch (e, st) {
      _logger.e('[_saveAlarm] 오류 발생: $e', error: e, stackTrace: st);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('알람 저장 중 오류가 발생했습니다: $e')),
      );
    } finally {
      _logger.d('[_saveAlarm] finally 진입 -> isLoading=false');
      setState(() => isLoading = false);
      _logger.d('[_saveAlarm] finally 종료');
    }
  }

  // --------------------------------------
  // Helper: Prepare Alarm Data
  // -------------------------------------용
  Future<Model> _prepareAlarmData() async {
    _logger.i('[_prepareAlarmData] 알람 데이터 준비');
    Map<String, dynamic>? weatherData;

    // AI 알람이면 날씨 데이터 가져오기
    if (aiAlarm) {
      try {
        _logger.d('[_prepareAlarmData] AI 알람 -> 날씨 요청');
        weatherData = await WeatherService().getLatestWeatherData().timeout(
          const Duration(seconds: 3),
          onTimeout: () {
            _logger.w('[_prepareAlarmData] 날씨 데이터 요청 시간초과');
            return {};
          },
        );
        _logger.d('[_prepareAlarmData] 날씨 데이터: $weatherData');
      } catch (e, st) {
        _logger.e('[_prepareAlarmData] 날씨 요청 실패', error: e, stackTrace: st);
        weatherData = {};
      }
    }

    // 사운드 처리: 사용자 정의 사운드면 저장 후 경로 반환
    String alarmSound = selectedSound;
    try {
      if (!AlarmHelper.sounds.contains(alarmSound) &&
          !alarmSound.startsWith(AlarmHelper.customSoundPrefix)) {
        _logger.d('[_prepareAlarmData] 사용자 정의 사운드 -> 저장 시도');
        alarmSound = await AlarmHelper.saveCustomSound(alarmSound);
        _logger.d('[_prepareAlarmData] 사용자 정의 사운드 경로: $alarmSound');
      }
    } catch (e, st) {
      _logger.e('[_prepareAlarmData] 사운드 처리 오류', error: e, stackTrace: st);
      alarmSound = AlarmHelper.defaultAlarmSound;
    }

    // AI 알람이면 날씨 기반 사운드와 시간대 기반 사운드 사용
    _logger.d('[_prepareAlarmData] AI 알람 체크: $aiAlarm');
    if (aiAlarm) {
      try {
        _logger.d('[_prepareAlarmData] AI 알람 사운드 선택 시작');

        // 임시 ID 생성 (실제 저장 시에는 다른 ID가 사용됨)
        int tempId = widget.alarm?.id ?? Random().nextInt(1000000);

        // getAppropriateAlarmSound 내부에서 날씨 및 시간대별 사운드를 모두 시도하고,
        // 유효한 사운드 목록 중 무작위 선택
        String aiSound = await AlarmHelper.getAppropriateAlarmSound(Model(
          id: tempId,
          label: controller.text.trim().isEmpty ? '알람' : controller.text.trim(),
          dateTime: DateFormat('HH:mm').format(notificationTime),
          check: true,
          when: _makeWhenText(notificationTime),
          milliseconds: notificationTime.millisecondsSinceEpoch,
          aiAlarm: aiAlarm, // 사용자가 설정한 값 사용
          weatherType: weatherData?['SKY']?.toString() ?? '',
          weatherData: weatherData,
          repeatDays:
              List.generate(7, (index) => (repeatDays & (1 << index)) != 0),
          alarmSound: alarmSound,
          volume: alarmVolume,
        ));

        _logger.d('[_prepareAlarmData] AI 알람 사운드 선택 결과: $aiSound');
        _logger.d(
            '[_prepareAlarmData] 기본 사운드와 비교: ${AlarmHelper.defaultAlarmSound}');

        // AI 알람에 대해 유효한 사운드가 선택되면 이를 사용
        if (aiSound.isNotEmpty && aiSound != AlarmHelper.defaultAlarmSound) {
          alarmSound = aiSound;
          _logger.i('[_prepareAlarmData] AI 알람 사운드 적용: $alarmSound');
        } else {
          _logger.w('[_prepareAlarmData] AI 알람 사운드가 유효하지 않아 기본 사운드를 사용합니다');
          // AI 알람이지만 사운드 선택에 실패한 경우에도 AI 알람으로 저장
          _logger.w('[_prepareAlarmData] AI 알람 플래그는 유지하고 기본 사운드 사용');
        }
      } catch (e) {
        _logger.e('[_prepareAlarmData] AI 알람 사운드 선택 실패: $e');
        _logger.w('[_prepareAlarmData] AI 알람 플래그는 유지하고 기본 사운드 사용');
      }
    }

    // Model 생성
    final model = Model(
      id: widget.alarm?.id ?? Random().nextInt(1000000),
      label: controller.text.trim().isEmpty ? '알람' : controller.text.trim(),
      dateTime: DateFormat('HH:mm').format(notificationTime),
      check: true,
      when: _makeWhenText(notificationTime),
      milliseconds: notificationTime.millisecondsSinceEpoch,
      aiAlarm: aiAlarm,
      weatherType: weatherData?['SKY'] ?? '',
      weatherData: weatherData,
      repeatDays: List.generate(7, (index) => (repeatDays & (1 << index)) != 0),
      alarmSound: alarmSound,
      volume: alarmVolume,
    );

    _logger.i(
        '[_prepareAlarmData] 완성된 Model - aiAlarm: ${model.aiAlarm}, sound: ${model.alarmSound}');
    return model;
  }

  /// 반복 요일 or "X시간 후" 표시
  String _makeWhenText(DateTime time) {
    final repeatText = _getFormattedRepeatDays();
    if (repeatText.isNotEmpty) {
      // 요일 반복
      return repeatText;
    } else {
      // 단일 알람: "X시간 후" 또는 "X일 후 HH:mm"
      return _getFormattedTime(time);
    }
  }

  /// 선택된 요일들의 텍스트
  String _getFormattedRepeatDays() {
    final selectedDays = <String>[];
    for (int i = 0; i < _days.length; i++) {
      if ((repeatDays & (1 << i)) != 0) {
        selectedDays.add(_days[i]);
      }
    }
    return selectedDays.join(', ');
  }

  /// "X시간 후" 등 가공
  String _getFormattedTime(DateTime time) {
    final now = DateTime.now();
    Duration diff = time.difference(now);
    if (diff.isNegative) {
      diff += const Duration(days: 1);
    }

    if (diff.inDays > 0) {
      return '${diff.inDays}일 후 ${DateFormat('HH:mm').format(time)}';
    } else if (diff.inHours > 0) {
      return '${diff.inHours}시간 ${diff.inMinutes % 60}분 후';
    } else if (diff.inMinutes > 0) {
      return '${diff.inMinutes}분 후';
    } else {
      return '1분 이내';
    }
  }

  /// UI 표시용 사운드 이름 포맷팅
  String _formatSoundName(String soundPath) {
    var fileName = soundPath.split('/').last;
    fileName = fileName
        .replaceAll('.ogg', '')
        .replaceAll('.mp3', '')
        .replaceAll('_', ' ');

    return fileName
        .split(' ')
        .map((word) =>
            word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
        .join(' ');
  }
}
