import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static Future<void> initialize() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await _notificationsPlugin.initialize(initializationSettings);
  }

  static Future<void> createNotificationChannels() async {
    const channels = [
      AndroidNotificationChannel(
        'silent_channel',
        'Silent Notifications',
        description: 'This channel is used for silent notifications.',
        importance: Importance.low,
        playSound: false,
      ),
      AndroidNotificationChannel(
        'alarm_channel',
        '알람',
        description: '알람을 위한 채널',
        importance: Importance.max,
        playSound: false,
        enableVibration: true,
      ),
    ];

    final androidPlatform =
        _notificationsPlugin.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();

    if (androidPlatform != null) {
      for (var channel in channels) {
        await androidPlatform.createNotificationChannel(channel);
      }
    }
  }

  static Future<void> showAlarmNotificationWithFullScreen(
      int id, String title, String body) async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'alarm_channel',
        '알람',
        channelDescription: '알람을 위한 채널',
        importance: Importance.max,
        priority: Priority.max,
        showWhen: true,
        icon: '@mipmap/ic_launcher',
        fullScreenIntent: true,
        playSound: false,
        sound: null,
        enableVibration: true,
        styleInformation: BigTextStyleInformation(''),
        category: AndroidNotificationCategory.alarm,
        visibility: NotificationVisibility.public,
      );

      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);

      await _notificationsPlugin.show(
        id,
        title,
        body,
        platformChannelSpecifics,
        payload: id.toString(),
      );
    } catch (e) {
      print('Failed to show alarm notification: $e');
    }
  }

  static Future<void> showSilentNotification(
      int id, String title, String body) async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'silent_channel',
        'Silent Notifications',
        channelDescription: 'This notification will not make any sound.',
        importance: Importance.low,
        priority: Priority.low,
        playSound: false,
        sound: null,
      );

      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);

      await _notificationsPlugin.show(
        id,
        title,
        body,
        platformChannelSpecifics,
      );
    } catch (e) {
      print('Failed to show silent notification: $e');
    }
  }

  static Future<void> cancelNotification(int id) async {
    await _notificationsPlugin.cancel(id);
  }
}
