import 'package:flutter/material.dart';
import 'package:flutter_voice_alarm/services/database_service.dart';

class AlarmVoicePicker extends StatefulWidget {
  const AlarmVoicePicker({super.key});

  @override
  State<AlarmVoicePicker> createState() => _AlarmVoicePickerState();
}

class _AlarmVoicePickerState extends State<AlarmVoicePicker> {
  final DatabaseService _databaseService = DatabaseService();
  String _selectedVoice = 'v01'; // 기본값

  @override
  void initState() {
    super.initState();
    _loadSelectedVoice();
  }

  Future<void> _loadSelectedVoice() async {
    final voice = await _databaseService.getSetting('alarmVoice');
    if (voice != null) {
      setState(() {
        _selectedVoice = voice['voice'] ?? 'v01';
      });
    }
  }

  Future<void> _saveSelectedVoice(String voice) async {
    await _databaseService.saveSetting('alarmVoice', {'voice': voice});
    setState(() {
      _selectedVoice = voice;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '알람 보이스 설정',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: RadioListTile<String>(
                title: const Text('알람 보이스 1'),
                value: 'v01',
                groupValue: _selectedVoice,
                onChanged: (value) => _saveSelectedVoice(value!),
              ),
            ),
            Expanded(
              child: RadioListTile<String>(
                title: const Text('알람 보이스 2'),
                value: 'v02',
                groupValue: _selectedVoice,
                onChanged: (value) => _saveSelectedVoice(value!),
              ),
            ),
          ],
        ),
        const Divider(),
      ],
    );
  }
}
