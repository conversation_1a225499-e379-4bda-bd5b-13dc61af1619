name: flutter_voice_alarm
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=3.4.1 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  flutter_dotenv: ^5.1.0
  geolocator: ^12.0.0
  provider: ^6.1.2
  flutter_local_notifications: ^17.2.2
  permission_handler: ^11.3.1
  http: ^1.2.1
  intl: ^0.19.0
  shared_preferences: ^2.2.3
  timezone: ^0.9.3
  klc: ^0.1.0
  audioplayers: ^6.0.0
  audio_session: ^0.1.21
  path: ^1.9.0
  sqflite: ^2.3.3+1
  logger: ^2.4.0
  connectivity_plus: ^6.0.5
  moon_phase_plus: ^1.0.1+6
  package_info_plus: ^8.1.0
  file_picker: ^8.1.4
  path_provider: ^2.1.5
  fluttertoast: ^8.2.10
  weather_icons: ^3.0.0
  get_it: ^8.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  assets:
    - .env
    - assets/sounds/
    - assets/sounds/voice1/
    - assets/sounds/voice2/
    - android/app/src/main/res/raw/lifes_good_alarm.ogg
    - android/app/src/main/res/raw/blossom.ogg
    - android/app/src/main/res/raw/like_a_movie.ogg
