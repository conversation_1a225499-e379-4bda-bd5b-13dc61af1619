import 'dart:io';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:flutter_voice_alarm/models/model.dart';
import 'package:flutter_voice_alarm/services/weather_service.dart';
import 'package:flutter_voice_alarm/services/database_service.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:timezone/data/latest.dart' as tz;

class AlarmHelper {
  static final Random _random = Random();
  static final WeatherService _weatherService = WeatherService();
  static final DatabaseService _databaseService = DatabaseService();
  static const String defaultAlarmSound = 'lifes_good_alarm';
  static const String customSoundPrefix = 'custom_';
  static const String assetPath = 'assets/sounds/';
  static const String rawPath = 'raw/';

  // 기본 알람 사운드 목록 (확장자 포함)
  static const List<String> sounds = [
    'lifes_good_alarm.ogg',
    'blossom.ogg',
    'like_a_movie.ogg'
  ];

  // 사용자 정의 사운드를 저장할 디렉토리 가져오기
  static Future<Directory> get _customSoundsDirectory async {
    final appDir = await getApplicationDocumentsDirectory();
    final customSoundsDir = Directory('${appDir.path}/custom_sounds');
    if (!await customSoundsDir.exists()) {
      await customSoundsDir.create(recursive: true);
    }
    return customSoundsDir;
  }

  static Future<String> saveCustomSound(String sourcePath) async {
    try {
      // 커스텀 사운드 디렉토리 생성
      final customDir = await _customSoundsDirectory;

      // 소스 파일이 assets인 경우
      if (!sourcePath.startsWith('/')) {
        final ByteData data =
            await rootBundle.load('assets/sounds/$sourcePath.ogg');
        final fileName = '$customSoundPrefix${basename(sourcePath)}.ogg';
        final targetPath = '${customDir.path}/$fileName';

        await File(targetPath).writeAsBytes(data.buffer.asUint8List());
        Logger().d('Custom sound saved from assets: $targetPath');
        return targetPath;
      }

      // 외부 파일인 경우
      final fileName = '$customSoundPrefix${basename(sourcePath)}';
      final targetPath = '${customDir.path}/$fileName';

      // 기존 파일이 있다면 덮어쓰기
      await File(sourcePath).copy(targetPath);

      Logger().d('Custom sound saved: $targetPath');
      return targetPath;
    } catch (e) {
      Logger().e('Error saving custom sound: $e');
      return defaultAlarmSound;
    }
  }

  // 날씨에 따른 알람 사운드
  static const Map<String, List<String>> weatherAlarmSounds = {
    'rain': ['sca_kr024_v01_w009_wv1.ogg', 'sca_kr024_v01_w009_wv2.ogg'],
    'snow': ['sca_kr024_v01_w001_wv1.ogg', 'sca_kr024_v01_w001_wv2.ogg'],
    'sunny': ['sca_kr024_v01_w014_wv1.ogg', 'sca_kr024_v01_w014_wv2.ogg'],
    'wind': ['sca_kr024_v01_w002_wv1.ogg', 'sca_kr024_v01_w002_wv2.ogg'],
    'heatwave': ['sca_kr024_v01_w013_wv1.ogg', 'sca_kr024_v01_w013_wv2.ogg'],
  };

  // 요일에 따른 알람 사운드 (오전, 점심, 오후, 밤)
  static const Map<String, List<String>> dayTimeAlarmSounds = {
    'mon': [
      'sca_kr024_v01_mo_mon_m.ogg', // 오전
      'sca_kr024_v01_mo_mon_n.ogg', // 오전
      'sca_kr024_v01_no_mon_m.ogg', // 점심
      'sca_kr024_v01_no_mon_n.ogg', // 점심
      'sca_kr024_v01_af_mon_m.ogg', // 오후
      'sca_kr024_v01_af_mon_n.ogg', // 오후
      'sca_kr024_v01_ni_mon_m.ogg', // 밤
      'sca_kr024_v01_ni_mon_n.ogg', // 밤
    ],
    'tue': [
      'sca_kr024_v01_mo_tue_m.ogg',
      'sca_kr024_v01_mo_tue_n.ogg',
      'sca_kr024_v01_no_tue_m.ogg',
      'sca_kr024_v01_no_tue_n.ogg',
      'sca_kr024_v01_af_tue_m.ogg',
      'sca_kr024_v01_af_tue_n.ogg',
      'sca_kr024_v01_ni_tue_m.ogg',
      'sca_kr024_v01_ni_tue_n.ogg',
    ],
    'wed': [
      'sca_kr024_v01_mo_wed_m.ogg',
      'sca_kr024_v01_mo_wed_n.ogg',
      'sca_kr024_v01_no_wed_m.ogg',
      'sca_kr024_v01_no_wed_n.ogg',
      'sca_kr024_v01_af_wed_m.ogg',
      'sca_kr024_v01_af_wed_n.ogg',
      'sca_kr024_v01_ni_wed_m.ogg',
      'sca_kr024_v01_ni_wed_n.ogg',
    ],
    'thu': [
      'sca_kr024_v01_mo_thu_m.ogg',
      'sca_kr024_v01_mo_thu_n.ogg',
      'sca_kr024_v01_no_thu_m.ogg',
      'sca_kr024_v01_no_thu_n.ogg',
      'sca_kr024_v01_af_thu_m.ogg',
      'sca_kr024_v01_af_thu_n.ogg',
      'sca_kr024_v01_ni_thu_m.ogg',
      'sca_kr024_v01_ni_thu_n.ogg',
    ],
    'fri': [
      'sca_kr024_v01_mo_fri_m.ogg',
      'sca_kr024_v01_mo_fri_n.ogg',
      'sca_kr024_v01_no_fri_m.ogg',
      'sca_kr024_v01_no_fri_n.ogg',
      'sca_kr024_v01_af_fri_m.ogg',
      'sca_kr024_v01_af_fri_n.ogg',
      'sca_kr024_v01_ni_fri_m.ogg',
      'sca_kr024_v01_ni_fri_n.ogg',
    ],
    'sat': [
      'sca_kr024_v01_mo_sat_m.ogg',
      'sca_kr024_v01_mo_sat_n.ogg',
      'sca_kr024_v01_no_sat_m.ogg',
      'sca_kr024_v01_no_sat_n.ogg',
      'sca_kr024_v01_af_sat_m.ogg',
      'sca_kr024_v01_af_sat_n.ogg',
      'sca_kr024_v01_ni_sat_m.ogg',
      'sca_kr024_v01_ni_sat_n.ogg',
    ],
    'sun': [
      'sca_kr024_v01_mo_sun_m.ogg',
      'sca_kr024_v01_mo_sun_n.ogg',
      'sca_kr024_v01_no_sun_m.ogg',
      'sca_kr024_v01_no_sun_n.ogg',
      'sca_kr024_v01_af_sun_m.ogg',
      'sca_kr024_v01_af_sun_n.ogg',
      'sca_kr024_v01_ni_sun_m.ogg',
      'sca_kr024_v01_ni_sun_n.ogg',
    ],
  };

  // 특별한 날의 알람 소리
  static const Map<String, String> specialDayAlarmSounds = {
    'newYear': 'sca_kr024_v01_h02_hv1.ogg',
    'newYearAlt': 'sca_kr024_v01_h02_hv2.ogg',
    'lunarNewYear': 'sca_kr024_v01_h03_hv1.ogg',
    'chuseok': 'sca_kr024_v01_h01_hv1.ogg',
    'christmas': 'sca_kr024_v01_h04_hv1.ogg',
    'christmasAlt': 'sca_kr024_v01_h04_hv2.ogg',
    'birthday': 'sca_kr024_v01_h05_hv1.ogg',
  };

  // 선택된 알람 보이스 가져오기
  static Future<String> getSelectedVoice() async {
    final voiceData = await _databaseService.getSetting('alarmVoice');
    return voiceData?['voice'] ?? 'v01';
  }

  // 알람 사운드 경로 생성 헬퍼 메서드
  static String _createSoundPath(String fileName, String selectedVoice) {
    final voiceFolder = selectedVoice == 'v01' ? 'voice1' : 'voice2';
    return '$voiceFolder/${fileName.replaceAll('v01', selectedVoice)}';
  }

  // 요일/시간대별 사운드 선택
  static Future<String> getDayTimeSound(DateTime alarmTime) async {
    final day = alarmTime.weekday;
    final hour = alarmTime.hour;
    final selectedVoice = await getSelectedVoice();

    String dayKey = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'][day % 7];
    List<String> daySounds = dayTimeAlarmSounds[dayKey] ?? [];

    if (daySounds.isEmpty) {
      return _createSoundPath('sca_kr024_v01_mo_mon_m.ogg', selectedVoice);
    }

    // 시간대별 인덱스 결정
    int timeIndex = switch (hour) {
      < 12 => 0, // 오전
      >= 12 && < 14 => 2, // 점심
      >= 14 && < 18 => 4, // 오후
      _ => 6, // 밤
    };

    // 성별 랜덤 선택 (남성/여성)
    int genderOffset = _random.nextInt(2); // 0 또는 1
    int finalIndex = timeIndex + genderOffset;

    // 인덱스 범위 확인
    if (finalIndex >= daySounds.length) {
      finalIndex = timeIndex; // 기본값으로 되돌림
    }

    return _createSoundPath(daySounds[finalIndex], selectedVoice);
  }

  // 날씨에 따른 알람 사운드 가져오기
  static Future<String> getWeatherSound(
      Map<String, dynamic> weatherData) async {
    Logger().d('Getting weather sound for data: $weatherData');
    final skyCondition = weatherData['SKY']?.toString();
    final ptyCondition = weatherData['PTY']?.toString(); // 강수형태
    final selectedVoice = await getSelectedVoice();

    String? soundPath;

    // 강수형태 우선 확인 (PTY: 0=없음, 1=비, 2=비/눈, 3=눈, 4=소나기)
    if (ptyCondition != null && ptyCondition != '0') {
      switch (ptyCondition) {
        case '1': // 비
        case '4': // 소나기
          List<String>? rainSounds = weatherAlarmSounds['rain'];
          if (rainSounds != null && rainSounds.isNotEmpty) {
            soundPath = rainSounds[_random.nextInt(rainSounds.length)];
            Logger().d('Selected rain sound for PTY: $ptyCondition');
          }
          break;
        case '2': // 비/눈
        case '3': // 눈
          List<String>? snowSounds = weatherAlarmSounds['snow'];
          if (snowSounds != null && snowSounds.isNotEmpty) {
            soundPath = snowSounds[_random.nextInt(snowSounds.length)];
            Logger().d('Selected snow sound for PTY: $ptyCondition');
          }
          break;
      }
    } else {
      // 강수가 없는 경우 하늘 상태로 판단 (SKY: 1=맑음, 3=구름많음, 4=흐림)
      switch (skyCondition) {
        case '1': // 맑음
          List<String>? sunnySounds = weatherAlarmSounds['sunny'];
          if (sunnySounds != null && sunnySounds.isNotEmpty) {
            soundPath = sunnySounds[_random.nextInt(sunnySounds.length)];
            Logger().d('Selected sunny sound for SKY: $skyCondition');
          }
          break;
        case '3': // 구름많음
        case '4': // 흐림
          List<String>? windSounds = weatherAlarmSounds['wind'];
          if (windSounds != null && windSounds.isNotEmpty) {
            soundPath = windSounds[_random.nextInt(windSounds.length)];
            Logger().d('Selected wind sound for SKY: $skyCondition');
          }
          break;
        default:
          Logger().d('Unknown SKY condition: $skyCondition');
          soundPath = null;
      }
    }

    if (soundPath != null) {
      String result = _createSoundPath(soundPath, selectedVoice);
      Logger().d('Final weather sound: $result');
      return result;
    }

    Logger().d('No weather sound found, returning default');
    return defaultAlarmSound;
  }

  // 알람 사운드 결정 (AI 알람 포함)
  static Future<String> getAppropriateAlarmSound(Model alarmModel) async {
    Logger().d('Getting appropriate sound for alarm: ${alarmModel.alarmSound}');
    if (alarmModel.aiAlarm) {
      DateTime alarmTime =
          DateTime.fromMillisecondsSinceEpoch(alarmModel.milliseconds);

      Logger().d('AI Alarm processing for time: ${alarmTime.toString()}');

      List<String> validSounds = [];

      // 1. 특별한 날 확인 (최우선)
      try {
        String? specialSound = await getSpecialDaySound(alarmTime);
        if (specialSound != null && specialSound != defaultAlarmSound) {
          validSounds.add(specialSound);
          Logger().d('Added special day sound: $specialSound');
        }
      } catch (e) {
        Logger().w('Special day sound selection failed: $e');
      }

      // 2. 생일 확인
      try {
        String? birthdaySound = await _checkBirthdaySound(alarmTime);
        if (birthdaySound != null && birthdaySound != defaultAlarmSound) {
          validSounds.add(birthdaySound);
          Logger().d('Added birthday sound: $birthdaySound');
        }
      } catch (e) {
        Logger().w('Birthday sound selection failed: $e');
      }

      // 3. 날씨 기반 사운드 (모든 시간대 적용)
      if (alarmModel.weatherData != null &&
          alarmModel.weatherData!.isNotEmpty) {
        try {
          String weatherSound = await getWeatherSound(alarmModel.weatherData!);
          if (weatherSound != defaultAlarmSound) {
            validSounds.add(weatherSound);
            Logger().d('Added weather-based sound: $weatherSound');
          }
        } catch (e) {
          Logger().w('Weather sound selection failed: $e');
        }
      }

      // 4. 시간대별 사운드 (항상 적용)
      try {
        String timeSound = await getDayTimeSound(alarmTime);
        if (timeSound != defaultAlarmSound) {
          validSounds.add(timeSound);
          Logger().d('Added time-based sound: $timeSound');
        }
      } catch (e) {
        Logger().w('Time-based sound selection failed: $e');
      }

      // 우선순위에 따라 사운드 선택
      if (validSounds.isNotEmpty) {
        // 특별한 날이나 생일이 있으면 우선 선택
        for (String sound in validSounds) {
          if (sound.contains('h01') ||
              sound.contains('h02') ||
              sound.contains('h03') ||
              sound.contains('h04') ||
              sound.contains('h05')) {
            Logger().d('Selected priority sound (special/birthday): $sound');
            return sound;
          }
        }

        // 그 외의 경우 랜덤 선택
        String selected = validSounds[_random.nextInt(validSounds.length)];
        Logger().d('Selected random AI sound: $selected');
        return selected;
      } else {
        Logger().w('No valid AI alarm sound found, using default');
        return defaultAlarmSound;
      }
    }

    // AI 알람이 아닌 경우
    if (alarmModel.alarmSound.startsWith('/')) {
      return alarmModel.alarmSound;
    }
    return alarmModel.alarmSound;
  }

  // 생일 사운드 확인 헬퍼 메서드
  static Future<String?> _checkBirthdaySound(DateTime alarmTime) async {
    try {
      Map<String, dynamic>? birthdayData = await getBirthday();
      if (birthdayData != null) {
        DateTime birthdayDate =
            DateTime.fromMillisecondsSinceEpoch(birthdayData['date']);

        // 생일인지 확인 (월, 일만 비교)
        if (alarmTime.month == birthdayDate.month &&
            alarmTime.day == birthdayDate.day) {
          final selectedVoice = await getSelectedVoice();
          String birthdaySound = _createSoundPath(
              specialDayAlarmSounds['birthday']!, selectedVoice);
          Logger().d('Today is birthday! Selected sound: $birthdaySound');
          return birthdaySound;
        }
      }
    } catch (e) {
      Logger().w('Error checking birthday: $e');
    }
    return null;
  }

  static Future<void> saveBirthday(String name, DateTime date) async {
    final Map<String, dynamic> birthdayData = {
      'name': name,
      'date': date.millisecondsSinceEpoch,
    };
    await _databaseService.saveSetting('birthday', birthdayData);
  }

  static Future<Map<String, dynamic>?> getBirthday() async {
    return await _databaseService.getSetting('birthday');
  }

  // 특별한 날 확인 및 처리
  static Future<String?> getSpecialDaySound(DateTime date) async {
    final selectedVoice = await getSelectedVoice();

    if (date.month == 1 && date.day == 1) {
      return _createSoundPath(
          _random.nextBool()
              ? specialDayAlarmSounds['newYear']!
              : specialDayAlarmSounds['newYearAlt']!,
          selectedVoice);
    } else if (date.month == 12 && date.day == 25) {
      return _createSoundPath(
          _random.nextBool()
              ? specialDayAlarmSounds['christmas']!
              : specialDayAlarmSounds['christmasAlt']!,
          selectedVoice);
    }
    return null;
  }

  // MethodChannel 설정
  static const MethodChannel platform =
      MethodChannel('flutter_voice_alarm/alarm_scheduler');

  // 초기화 메서드
  static Future<void> initialize() async {
    try {
      tz.initializeTimeZones();
      // 기타 초기화 코드 추가 가능
    } catch (e, stackTrace) {
      Logger().e("초기화 중 오류 발생", error: e, stackTrace: stackTrace);
    }
  }

  // 알람 스케줄링 메서드
  static Future<void> scheduleAlarm(Model alarmModel) async {
    await requestExactAlarmPermission();

    try {
      DateTime nextAlarmTime = _getNextValidAlarmTime(alarmModel);
      alarmModel = alarmModel.copyWith(
        milliseconds: nextAlarmTime.millisecondsSinceEpoch,
      );

      // AI 알람 처리 개선
      if (alarmModel.aiAlarm) {
        var weatherData = await _weatherService.getLatestWeatherData();
        alarmModel = alarmModel.copyWith(weatherData: weatherData);
        Logger().d('AI Alarm weather data: $weatherData');
      }

      String soundName = await getAppropriateAlarmSound(alarmModel);
      Logger().d('Final alarm sound name: $soundName');

      // 알람 데이터 업데이트
      alarmModel = alarmModel.copyWith(alarmSound: soundName);
      await _databaseService.updateAlarm(alarmModel);

      Map<String, dynamic> alarmData = {
        'alarmId': alarmModel.id,
        'alarmTime': alarmModel.milliseconds,
        'title': alarmModel.label,
        'body': '알람이 울립니다',
        'alarmSound': soundName,
        'isAIAlarm': alarmModel.aiAlarm,
        'volume': alarmModel.volume,
      };

      await platform.invokeMethod('scheduleAlarm', alarmData);
      Logger().d('Successfully scheduled alarm with ID: ${alarmModel.id}');
    } catch (e) {
      Logger().e('Failed to schedule alarm: $e');
      rethrow;
    }
  }

  // 알람 취소 메서드
  static Future<void> cancelAlarm(int alarmId) async {
    try {
      Logger().d('Canceling alarm with ID: $alarmId');

      // 네이티브 코드로 알람 취소 요청
      await platform.invokeMethod('cancelAlarm', {'alarmId': alarmId});

      // 데이터베이스에서 알람 정보 가져오기
      Model? alarm = await _databaseService.getAlarm(alarmId);
      if (alarm != null) {
        // 알람이 반복 알람이 아닌 경우에만 비활성화
        if (!alarm.repeatDays.contains(true) && !alarm.when.contains('매년')) {
          final updatedAlarm = alarm.copyWith(check: false);
          await _databaseService.updateAlarm(updatedAlarm);
        }
      }

      Logger().d('Successfully canceled alarm with ID: $alarmId');
    } catch (e) {
      Logger().e('Failed to cancel alarm: $e');
      rethrow;
    }
  }

  // 알람 해제 메서드
  static Future<void> dismissAlarm(int alarmId) async {
    try {
      await platform.invokeMethod('dismissAlarm', {'alarmId': alarmId});
    } catch (e) {
      Logger().e('알람 해제 중 오류 발생: $e');
    }
  }

  // 스누즈 알람 메서드
  static Future<void> snoozeAlarm(Model alarm, int snoozeMinutes) async {
    try {
      await cancelAlarm(alarm.id);

      // 스누즈 시간으로 새로운 알람 모델 생성
      DateTime snoozeDateTime =
          DateTime.now().add(Duration(minutes: snoozeMinutes));
      Model snoozeAlarm = alarm.copyWith(
        milliseconds: snoozeDateTime.millisecondsSinceEpoch,
      );

      await scheduleAlarm(snoozeAlarm);
      Logger().i('알람 스누즈 성공');
    } catch (e) {
      Logger().e('알람 스누즈 실패: $e');
    }
  }

  // 다음 유효한 알람 시간 계산
  static DateTime _getNextValidAlarmTime(Model alarm) {
    final now = DateTime.now();
    final alarmDateTime =
        DateTime.fromMillisecondsSinceEpoch(alarm.milliseconds);

    // 매년 반복 알람 처리
    if (alarm.when.contains('매년')) {
      var nextDate = DateTime(
        now.year,
        alarmDateTime.month,
        alarmDateTime.day,
        alarmDateTime.hour,
        alarmDateTime.minute,
      );
      if (nextDate.isBefore(now)) {
        nextDate = DateTime(
          now.year + 1,
          alarmDateTime.month,
          alarmDateTime.day,
          alarmDateTime.hour,
          alarmDateTime.minute,
        );
      }
      return nextDate;
    } else if (alarm.repeatDays.contains(true)) {
      // 요일 반복 알람
      var nextDate = alarmDateTime;
      while (
          nextDate.isBefore(now) || !alarm.repeatDays[nextDate.weekday - 1]) {
        nextDate = nextDate.add(const Duration(days: 1));
      }
      return nextDate;
    } else {
      // 일회성 알람
      return alarmDateTime.isBefore(now)
          ? alarmDateTime.add(const Duration(days: 1))
          : alarmDateTime;
    }
  }

  // 정확한 알람 권한 요청
  static Future<void> requestExactAlarmPermission() async {
    if (await Permission.ignoreBatteryOptimizations.isDenied) {
      await Permission.ignoreBatteryOptimizations.request();
    }
    await _checkExactAlarmPermission();
  }

  static Future<bool> _checkExactAlarmPermission() async {
    const platform = MethodChannel('flutter_voice_alarm/exact_alarm');
    try {
      return await platform.invokeMethod('checkExactAlarmPermission');
    } on PlatformException catch (e) {
      Logger().i("정확한 알람 권한 확인 실패: '${e.message}'.");
      return false;
    }
  }

  // 활성화된 알람만 가져오는 메서드
  static Future<List<Model>> getActiveAlarms() async {
    List<Model> allAlarms = await _databaseService.getAlarms();
    final now = DateTime.now().millisecondsSinceEpoch;
    return allAlarms
        .where((alarm) =>
            alarm.check && // 활성화된 알람만
            (alarm.milliseconds > now ||
                _isRecurringAlarm(alarm))) // 현재 시간 이후거나 반복 알람
        .toList();
  }

  // 반복 알람 체크 헬퍼 메서드
  static bool _isRecurringAlarm(Model alarm) {
    return alarm.repeatDays.contains(true) || alarm.when.contains('매년');
  }

  static Future<Model?> getAlarm(int alarmId) async {
    return await _databaseService.getAlarm(alarmId);
  }

  static Future<void> clearAllAlarms() async {
    await _databaseService.clearAllAlarms();
  }

  static Future<void> updateAlarm(Model alarmModel) async {
    await cancelAlarm(alarmModel.id);
    await scheduleAlarm(alarmModel);
  }

  // 사용자 설정 관리 메서드
  static Future<void> setUserSnoozeTime(int minutes) async {
    await _databaseService.setSnoozeTime(minutes);
  }

  static Future<void> setAlarmVolume(double volume) async {
    await _databaseService.setAlarmVolume(volume);
  }

  static Future<double> getAlarmVolume() async {
    return await _databaseService.getAlarmVolume() ?? 1.0;
  }

  // 알람 상태 확인 및 관리
  static Future<bool> isAlarmEnabled(int alarmId) async {
    Model? alarm = await _databaseService.getAlarm(alarmId);
    return alarm?.check ?? false;
  }

  // 알람 활성화/비활성화 토글 메서드
  static Future<void> toggleAlarmEnabled(int alarmId, bool isEnabled) async {
    Model? alarm = await _databaseService.getAlarm(alarmId);
    if (alarm != null) {
      if (isEnabled) {
        // 활성화 시 다음 유효한 알람 시간 계산
        DateTime nextAlarmTime = _getNextValidAlarmTime(alarm);
        alarm = alarm.copyWith(
          check: isEnabled,
          milliseconds: nextAlarmTime.millisecondsSinceEpoch,
        );
        await scheduleAlarm(alarm);
      } else {
        alarm = alarm.copyWith(check: isEnabled);
        await cancelAlarm(alarmId);
      }
      await _databaseService.updateAlarm(alarm);
    }
  }

  // 요일별 알람 가져오기
  static Future<List<Model>> getAlarmsForDay(int weekday) async {
    List<Model> allAlarms = await getActiveAlarms();
    return allAlarms
        .where((alarm) => alarm.when.contains(_weekdayToString(weekday)))
        .toList();
  }

  static String _weekdayToString(int weekday) {
    const List<String> weekdays = ['월', '화', '수', '목', '금', '토', '일'];
    return weekdays[weekday - 1];
  }

  // 재부팅 시 알람 재설정
  static Future<void> rescheduleAllAlarms() async {
    try {
      List<Model> activeAlarms = await getActiveAlarms();

      for (var alarm in activeAlarms) {
        if (alarm.check) {
          // 다음 유효한 알람 시간 계산
          DateTime nextAlarmTime = _getNextValidAlarmTime(alarm);
          Model updatedAlarm = alarm.copyWith(
            milliseconds: nextAlarmTime.millisecondsSinceEpoch,
          );

          await scheduleAlarm(updatedAlarm);
        }
      }
      Logger().i('모든 알람 재스케줄링 완료');
    } catch (e) {
      Logger().e('알람 재스케줄링 실패: $e');
    }
  }

  // 특별한 알람 설정
  static Future<void> setBirthdayAlarm(DateTime birthday, String name) async {
    final now = DateTime.now();
    final nextBirthday = DateTime(now.year, birthday.month, birthday.day);
    final alarmTime = nextBirthday.isBefore(now)
        ? nextBirthday.add(const Duration(days: 365))
        : nextBirthday;

    final selectedVoice = await getSelectedVoice();
    final birthdaySound =
        _createSoundPath(specialDayAlarmSounds['birthday']!, selectedVoice);

    final birthdayAlarm = Model(
      id: Random().nextInt(1000000),
      label: '$name의 생일',
      dateTime: DateFormat('HH:mm').format(alarmTime),
      check: true,
      when: '매년',
      milliseconds: alarmTime.millisecondsSinceEpoch,
      aiAlarm: false,
      alarmSound: birthdaySound,
      repeatDays: List<bool>.filled(7, false),
      volume: await getAlarmVolume(),
    );

    await scheduleAlarm(birthdayAlarm);
  }

  // 알람 가져오기/내보내기
  static Future<void> importAlarms(List<Model> alarms) async {
    for (var alarm in alarms) {
      await _databaseService.insertAlarm(alarm);
    }
    await rescheduleAllAlarms();
  }

  static Future<List<Model>> exportAlarms() async {
    return await getActiveAlarms();
  }

  // 선택된 소리가 sounds 목록에 있는지 확인하고 반환
  static String getSelectedSound(String selectedSound) {
    if (sounds.contains(selectedSound)) {
      return selectedSound;
    }
    return defaultAlarmSound;
  }
}
