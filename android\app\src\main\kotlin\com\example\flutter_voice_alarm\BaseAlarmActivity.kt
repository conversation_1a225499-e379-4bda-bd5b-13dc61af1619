package com.example.flutter_voice_alarm


import android.app.Activity
import android.content.Context
import android.media.AudioManager
import android.media.MediaPlayer
import android.os.Build
import android.util.Log
import android.view.WindowManager
import java.io.File

abstract class BaseAlarmActivity : Activity() {
    protected var mediaPlayer: MediaPlayer? = null
    protected lateinit var audioManager: AudioManager
    protected var originalVolume: Int = 0
    protected open val TAG = "BaseAlarmActivity"

    protected fun initAudio() {
        audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
        // 알람 스트림 볼륨을 저장 (무음 모드에서도 작동)
        originalVolume = audioManager.getStreamVolume(AudioManager.STREAM_ALARM)
        Log.d(TAG, "Original alarm volume: $originalVolume")
    }

    protected fun setAlarmVolume(volume: Float) {
        try {
            // 알람 스트림 사용 (무음 모드에서도 작동)
            val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_ALARM)
            val alarmVolume = (maxVolume * volume).toInt().coerceAtLeast(1) // 최소 1로 설정

            // 무음 모드 확인 및 강제 볼륨 설정
            val ringerMode = audioManager.ringerMode
            Log.d(TAG, "Current ringer mode: $ringerMode")

            if (ringerMode == AudioManager.RINGER_MODE_SILENT || ringerMode == AudioManager.RINGER_MODE_VIBRATE) {
                Log.d(TAG, "Device is in silent/vibrate mode, forcing alarm volume")
                // 무음/진동 모드에서도 알람 볼륨 강제 설정
                audioManager.setStreamVolume(AudioManager.STREAM_ALARM, alarmVolume, AudioManager.FLAG_ALLOW_RINGER_MODES)
            } else {
                audioManager.setStreamVolume(AudioManager.STREAM_ALARM, alarmVolume, 0)
            }

            Log.d(TAG, "Alarm volume set to: $alarmVolume/$maxVolume (${volume * 100}%)")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set alarm volume", e)
        }
    }

    protected fun restoreVolume() {
        try {
            audioManager.setStreamVolume(AudioManager.STREAM_ALARM, originalVolume, 0)
            Log.d(TAG, "Alarm volume restored to original: $originalVolume")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to restore volume", e)
        }
    }

    protected fun wakeUpScreen() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true)
            setTurnScreenOn(true)
            val keyguardManager = getSystemService(Context.KEYGUARD_SERVICE) as android.app.KeyguardManager
            keyguardManager.requestDismissKeyguard(this, null)
        }
        window.addFlags(
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                    WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                    WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON or
                    WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
                    WindowManager.LayoutParams.FLAG_ALLOW_LOCK_WHILE_SCREEN_ON
        )
    }

    protected fun loadDefaultFromRaw(volume: Float) {
        try {
            releaseMediaPlayer()
            mediaPlayer = MediaPlayer().apply {
                // 알람 스트림 타입 설정 (무음 모드에서도 작동)
                setAudioStreamType(AudioManager.STREAM_ALARM)

                val resourceId = resources.getIdentifier("lifes_good_alarm", "raw", packageName)
                if (resourceId != 0) {
                    setDataSource(
                        applicationContext,
                        android.net.Uri.parse("android.resource://$packageName/$resourceId")
                    )
                    isLooping = true
                    setVolume(volume, volume)
                    prepare()
                    start()
                    Log.d(TAG, "Successfully loaded default sound from raw resource")
                } else {
                    Log.e(TAG, "Could not find default sound in raw resources")
                    finish()
                }
            }
            setAlarmVolume(volume)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading default sound from raw", e)
            finish()
        }
    }

    protected fun loadDefaultSound(volume: Float) {
        try {
            loadDefaultFromRaw(volume)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading default sound", e)
            finish()
        }
    }

    private fun releaseMediaPlayer() {
        mediaPlayer?.apply {
            try {
                if (isPlaying) {
                    stop()
                }
                reset()
                release()
            } catch (e: Exception) {
                Log.e(TAG, "Error releasing MediaPlayer", e)
            } finally {
                // MediaPlayer 참조를 즉시 null로 설정하여 메모리 누수 방지
                mediaPlayer = null
            }
        }
        mediaPlayer = null
    }

    protected fun initializeMediaPlayerCommon(
        volume: Float,
        alarmSound: String,
        assetPathResolver: (String) -> String
    ) {
        try {
            Log.d(TAG, "Initializing MediaPlayer with sound: $alarmSound, volume: $volume")
            releaseMediaPlayer()
            mediaPlayer = MediaPlayer().apply {
                // 알람 스트림 타입 설정 (무음 모드에서도 작동)
                setAudioStreamType(AudioManager.STREAM_ALARM)
                // 웨이크락 설정 (화면이 꺼져도 재생 유지)
                setWakeMode(applicationContext, android.os.PowerManager.PARTIAL_WAKE_LOCK)
            }

            when {
                alarmSound.startsWith("custom_") -> {
                    val customSoundsDir = File(applicationContext.filesDir, "custom_sounds")
                    if (!customSoundsDir.exists()) customSoundsDir.mkdirs()
                    val soundFile = File(customSoundsDir, alarmSound)
                    if (soundFile.exists()) {
                        mediaPlayer?.setDataSource(soundFile.absolutePath)
                    } else {
                        loadDefaultSound(volume)
                        return
                    }
                }
                alarmSound.startsWith("/") -> {
                    val soundFile = File(alarmSound)
                    if (soundFile.exists()) {
                        mediaPlayer?.setDataSource(alarmSound)
                    } else {
                        loadDefaultSound(volume)
                        return
                    }
                }
                else -> {
                    val assetPath = assetPathResolver(alarmSound)
                    try {
                        Log.d(TAG, "🔔 Original alarm sound: $alarmSound")
                        Log.d(TAG, "🔔 Resolved asset path: $assetPath")

                        // Flutter assets 대신 직접 assets 폴더에서 로드
                        val assetManager = assets

                        // assets 폴더에 파일이 있는지 확인
                        val assetList = try {
                            assetManager.list("sounds/voice1") ?: emptyArray()
                        } catch (e: Exception) {
                            Log.e(TAG, "🔔 Error listing assets", e)
                            emptyArray()
                        }
                        Log.d(TAG, "🔔 Available voice1 assets: ${assetList.take(5).joinToString()}")

                        val afd = assetManager.openFd(assetPath)
                        mediaPlayer?.setDataSource(afd.fileDescriptor, afd.startOffset, afd.length)
                        afd.close()
                        Log.d(TAG, "🔔 Successfully loaded AI alarm sound: $assetPath")
                    } catch (e: Exception) {
                        Log.e(TAG, "🔔 Error loading voice sound from assets: $assetPath", e)
                        Log.e(TAG, "🔔 Falling back to default sound")
                        loadDefaultFromRaw(volume)
                        return
                    }
                }
            }

            mediaPlayer?.apply {
                isLooping = true
                setVolume(volume, volume)

                // 에러 리스너 설정
                setOnErrorListener { mp, what, extra ->
                    Log.e(TAG, "MediaPlayer error: what=$what, extra=$extra")
                    false // false를 반환하면 OnCompletionListener가 호출됨
                }

                // 준비 완료 리스너 설정
                setOnPreparedListener { mp ->
                    Log.d(TAG, "MediaPlayer prepared, starting playback")
                    try {
                        mp.start()
                        setAlarmVolume(volume)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error starting MediaPlayer", e)
                    }
                }

                prepareAsync() // 비동기 준비
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing MediaPlayer", e)
            loadDefaultFromRaw(volume)
        }
    }

    override fun onDestroy() {
        try {
            releaseMediaPlayer()
            restoreVolume()
        } catch (e: Exception) {
            Log.e(TAG, "Error in onDestroy", e)
        } finally {
            super.onDestroy()
        }
    }

    override fun onPause() {
        super.onPause()
        // 액티비티가 백그라운드로 갈 때 리소스 정리
        try {
            mediaPlayer?.pause()
        } catch (e: Exception) {
            Log.e(TAG, "Error pausing MediaPlayer", e)
        }
    }

    override fun onResume() {
        super.onResume()
        // 액티비티가 포그라운드로 올 때 재생 재개
        try {
            mediaPlayer?.start()
        } catch (e: Exception) {
            Log.e(TAG, "Error resuming MediaPlayer", e)
        }
    }
}