package com.example.flutter_voice_alarm

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import androidx.core.app.NotificationCompat
import android.app.KeyguardManager
import android.os.PowerManager
import android.util.Log

class NotificationHelper(context: Context) {
    companion object {
        private const val ALARM_CHANNEL = "com.example.flutter_voice_alarm/alarm_events"
        private const val NOTIFICATION_CHANNEL_ID = "ALARM_NOTIFICATIONS"
        private const val NOTIFICATION_CHANNEL_NAME = "Alarm Notifications"
    }

    // WeakReference를 사용하여 메모리 누수 방지
    private val contextRef = java.lang.ref.WeakReference(context)

    private val notificationManager: NotificationManager? by lazy {
        contextRef.get()?.getSystemService(Context.NOTIFICATION_SERVICE) as? NotificationManager
    }

    init {
        createNotificationChannel()
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            notificationManager?.let { manager ->
                val channel = NotificationChannel(
                    NOTIFICATION_CHANNEL_ID,
                    NOTIFICATION_CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "Notifications for alarms"
                    enableLights(true)
                    lightColor = Color.RED
                    enableVibration(true)
                    setShowBadge(true)
                }
                manager.createNotificationChannel(channel)
            }
        }
    }

    fun showAlarmNotification(
        alarmId: Int,
        title: String,
        message: String,
        volume: Float = 1.0f,
        alarmSound: String? = null,
        isAIAlarm: Boolean = false
    ) {
        val context = contextRef.get() ?: return
        val keyguardManager = context.getSystemService(Context.KEYGUARD_SERVICE) as? KeyguardManager ?: return
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as? PowerManager ?: return

        // 화면이 꺼져있거나 잠겨있으면 풀스크린 알람 실행
        if (keyguardManager.isKeyguardLocked || !powerManager.isInteractive) {
            val fullScreenIntent = Intent(context, FullScreenAlarmActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                putExtra("alarmId", alarmId)
                putExtra("title", title)
                putExtra("body", message)
                putExtra("volume", volume)
                putExtra("alarmSound", alarmSound)
                putExtra("isAIAlarm", isAIAlarm)
            }
            context.startActivity(fullScreenIntent)
            return
        }

        // 팝업 알람 Intent 설정
        val popupIntent = Intent(context, TopPopupAlarmActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
            putExtra("alarmId", alarmId)
            putExtra("title", title)
            putExtra("body", message)
            putExtra("volume", volume)
            putExtra("alarmSound", alarmSound)
            putExtra("isAIAlarm", isAIAlarm)
        }
        context.startActivity(popupIntent)

        // 알람 해제 Intent
        val dismissIntent = Intent(context, AlarmReceiver::class.java).apply {
            action = "DISMISS_ALARM"
            putExtra("alarmId", alarmId)
        }
        val dismissPendingIntent = PendingIntent.getBroadcast(
            context,
            alarmId * 2,
            dismissIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 알람 스누즈 Intent
        val snoozeIntent = Intent(context, AlarmReceiver::class.java).apply {
            action = "SNOOZE_ALARM"
            putExtra("alarmId", alarmId)
        }
        val snoozePendingIntent = PendingIntent.getBroadcast(
            context,
            alarmId * 2 + 1,
            snoozeIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 팝업 알람으로 돌아가기 위한 Intent
        val contentPendingIntent = PendingIntent.getActivity(
            context,
            alarmId,
            popupIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 알림 생성
        val notification = NotificationCompat.Builder(context, NOTIFICATION_CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_alarm_snooze)
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setContentIntent(contentPendingIntent)
            .addAction(R.drawable.ic_alarm_dismiss, "해제", dismissPendingIntent)
            .addAction(R.drawable.ic_alarm_snooze, "다시 울림", snoozePendingIntent)
            .setOngoing(true)  // 사용자가 옆으로 밀어서 삭제할 수 없도록 설정
            .setAutoCancel(false)  // 알림을 탭해도 자동으로 사라지지 않도록 설정
            .build()

        notificationManager?.notify(alarmId, notification)
    }

    fun cancelAlarmNotification(alarmId: Int) {
        notificationManager?.cancel(alarmId)
    }
}