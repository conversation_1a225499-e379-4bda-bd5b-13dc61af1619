package com.example.flutter_voice_alarm


import android.app.Activity
import android.app.KeyguardManager
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.media.MediaPlayer
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import androidx.constraintlayout.widget.ConstraintLayout
import android.view.animation.AccelerateDecelerateInterpolator
import android.animation.ObjectAnimator
import android.graphics.Color
import android.view.animation.AnimationUtils
import kotlinx.coroutines.*
import java.io.File
import java.lang.Exception
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.abs
import kotlin.math.sqrt

class FullScreenAlarmActivity : BaseAlarmActivity() {
    companion object {
        private const val TAG = "FullScreenAlarmActivity"
        private const val DRAG_THRESHOLD = 300f
    }

    private var alarmContainer: MaterialCardView? = null
    private var dismissButton: ImageView? = null
    private var snoozeButton: MaterialButton? = null
    private var alarmTitle: TextView? = null
    private var alarmBody: TextView? = null
    private var currentTimeTextView: TextView? = null
    private var currentDateTextView: TextView? = null
    private var dragHintText: TextView? = null
    private var weatherTemp: TextView? = null
    private var weatherIcon: ImageView? = null
    private var weatherStatus: TextView? = null
    private var weatherHumidity: TextView? = null

    private var alarmId: Int = -1
    private var dX = 0f
    private var dY = 0f
    private var originalX = 0f
    private var originalY = 0f

    private var handler: Handler? = null
    private var coroutineScope: CoroutineScope? = null
    private val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
    private val dateFormat = SimpleDateFormat("EEEE, MMMM d", Locale.getDefault())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 핸들러와 코루틴 스코프 초기화
        handler = Handler(Looper.getMainLooper())
        coroutineScope = CoroutineScope(Dispatchers.Main + Job())

        // Flutter 초기화 (알람 액티비티에서는 필요하지 않음)
        // FlutterLoader().ensureInitializationComplete(applicationContext, null)
        setContentView(R.layout.activity_full_screen_alarm)

        try {
            initAudio()
            wakeUpScreen()
            initializeViews()
            setupUI()
            initMediaPlayer()
        } catch (e: Exception) {
            Log.e(TAG, "Error in onCreate", e)
            cleanupAndFinish()
        }
    }

    private fun initializeViews() {
        try {
            alarmContainer = findViewById(R.id.alarmContainer)
            dismissButton = findViewById(R.id.dismissButton)
            snoozeButton = findViewById(R.id.snoozeButton)
            alarmTitle = findViewById(R.id.alarmTitle)
            alarmBody = findViewById(R.id.alarmBody)
            currentTimeTextView = findViewById(R.id.currentTime)
            currentDateTextView = findViewById(R.id.currentDate)
            dragHintText = findViewById(R.id.dragHintText)
            weatherTemp = findViewById(R.id.weatherTemp)
            weatherIcon = findViewById(R.id.weatherIcon)
            weatherStatus = findViewById(R.id.weatherStatus)
            weatherHumidity = findViewById(R.id.weatherHumidity)

            initWeather()
            alarmId = intent.getIntExtra("alarmId", -1)
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing views", e)
            cleanupAndFinish()
        }
    }

    private fun initWeather() {
        try {
            val weatherContainer = findViewById<View>(R.id.weatherContainer)
            val mainActivity = MainActivity.getInstance()
            val weatherData = mainActivity?.cachedWeatherData

            Log.d(TAG, "Weather data available: ${weatherData != null}")

            if (weatherData != null) {
                updateWeatherUI(weatherData)
            } else {
                weatherContainer?.visibility = View.GONE
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing weather", e)
            findViewById<View>(R.id.weatherContainer)?.visibility = View.GONE
        }
    }

    private fun setupUI() {
        alarmTitle?.text = intent.getStringExtra("title") ?: "알람"
        alarmBody?.text = intent.getStringExtra("body") ?: "일어날 시간입니다!"

        updateTimeAndDate()
        startTimeUpdate()

        dismissButton?.let { button ->
            val pulseAnimation = AnimationUtils.loadAnimation(this, R.anim.pulse_animation)
            button.startAnimation(pulseAnimation)
        }

        setupDismissButtonDrag()
        setupButtonListeners()
        startBackgroundAnimation()
    }

    private fun setupButtonListeners() {
        snoozeButton?.setOnClickListener {
            mediaPlayer?.let { player ->
                snoozeButton?.isEnabled = false
                snoozeButton?.let { button ->
                    ObjectAnimator.ofFloat(button, "alpha", 1f, 0f).apply {
                        duration = 300
                        start()
                    }
                }
                AlarmHelper.snoozeAlarm(
                    this,
                    alarmId,
                    player,
                    ::restoreVolume
                ) {
                    cleanupAndFinish()
                }
            }
        }

        dismissButton?.setOnClickListener {
            mediaPlayer?.let { player ->
                AlarmHelper.dismissAlarm(
                    this,
                    alarmId,
                    player,
                    ::restoreVolume
                ) {
                    cleanupAndFinish()
                }
            }
        }
    }

    private fun initMediaPlayer() {
        val volume = intent.getFloatExtra("volume", 1.0f)
        val alarmSound = intent.getStringExtra("alarmSound") ?: "lifes_good_alarm"
        Log.d(TAG, "Alarm sound: $alarmSound, volume: $volume")

        initializeMediaPlayerCommon(volume, alarmSound) { sound ->
            if (sound.contains("/")) {
                "assets/sounds/$sound"
            } else {
                "assets/sounds/voice2/$sound"
            }
        }
    }

    private fun updateTimeAndDate() {
        val now = Date()
        currentTimeTextView?.text = timeFormat.format(now)
        currentDateTextView?.text = dateFormat.format(now)
    }

    private fun startTimeUpdate() {
        val updateTimeRunnable = object : Runnable {
            override fun run() {
                updateTimeAndDate()
                val now = System.currentTimeMillis()
                val delay = 60000 - (now % 60000)
                handler?.postDelayed(this, delay)
            }
        }
        handler?.post(updateTimeRunnable)
    }

    private fun startBackgroundAnimation() {
        val rootLayout = findViewById<ConstraintLayout>(R.id.rootLayout)
        val animator = ObjectAnimator.ofFloat(rootLayout, "alpha", 0.8f, 1f)
        animator.duration = 2000
        animator.repeatMode = ObjectAnimator.REVERSE
        animator.repeatCount = ObjectAnimator.INFINITE
        animator.interpolator = AccelerateDecelerateInterpolator()
        animator.start()
    }

    private fun updateWeatherUI(weatherData: Map<String, Any>) {
        try {
            val temp = weatherData["T1H"] ?: weatherData["TMP"] ?: "?"
            val sky = weatherData["SKY"] ?: "1"
            val pty = weatherData["PTY"] ?: "0"
            val reh = weatherData["REH"] ?: "?"
            val rn1 = weatherData["RN1"] ?: "0"
            val sno = weatherData["SNO"] ?: "0"
            val source = weatherData["source"] as? String ?: "KMA"

            Log.d(TAG, "Weather source: $source, Temp: $temp, Sky: $sky, PTY: $pty")

            weatherTemp?.apply {
                text = "${temp}℃"
                setTextColor(Color.WHITE)
            }
            weatherHumidity?.apply {
                text = "습도 ${reh}%"
                setTextColor(Color.WHITE)
            }
            weatherStatus?.apply {
                text = when {
                    pty != "0" -> when (pty) {
                        "1" -> "비"
                        "2" -> "비/눈"
                        "3" -> "눈"
                        "4" -> "소나기"
                        "5" -> "빗방울"
                        "6" -> "빗방울/눈날림"
                        "7" -> "눈날림"
                        else -> ""
                    }
                    else -> when (sky) {
                        "1" -> "맑음"
                        "2" -> "구름조금"
                        "3" -> "구름많음"
                        "4" -> "흐림"
                        else -> ""
                    }
                }
                setTextColor(Color.WHITE)
            }

            weatherIcon?.layoutParams = weatherIcon?.layoutParams?.apply {
                width = resources.getDimensionPixelSize(R.dimen.weather_icon_size)
                height = resources.getDimensionPixelSize(R.dimen.weather_icon_size)
            }

            val weatherIconResId = when {
                pty != "0" -> when (pty) {
                    "1", "4" -> {
                        val rainAmount = when (source) {
                            "OpenWeather" -> rn1.toString().toFloatOrNull() ?: 0f
                            else -> rn1.toString().replace("mm", "").toFloatOrNull() ?: 0f
                        }
                        when {
                            rainAmount >= 30 -> R.drawable.rainy_6
                            rainAmount >= 15 -> R.drawable.rainy_5
                            rainAmount >= 5 -> R.drawable.rainy_4
                            rainAmount >= 3 -> R.drawable.rainy_3
                            rainAmount >= 1 -> R.drawable.rainy_2
                            else -> R.drawable.rainy_1
                        }
                    }
                    "2", "6" -> R.drawable.sleet_icon
                    "3", "7" -> {
                        val snowAmount = when (source) {
                            "OpenWeather" -> sno.toString().toFloatOrNull() ?: 0f
                            else -> sno.toString().replace("cm", "").toFloatOrNull() ?: 0f
                        }
                        when {
                            snowAmount >= 5 -> R.drawable.snowy_6
                            snowAmount >= 3 -> R.drawable.snowy_5
                            snowAmount >= 2 -> R.drawable.snowy_4
                            snowAmount >= 1 -> R.drawable.snowy_3
                            snowAmount >= 0.5 -> R.drawable.snowy_2
                            else -> R.drawable.snowy_1
                        }
                    }
                    else -> R.drawable.cloudy
                }
                else -> when (sky) {
                    "1" -> {
                        val hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)
                        if (hour in 6..17) R.drawable.day else R.drawable.night
                    }
                    "2", "3" -> {
                        val hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)
                        if (hour in 6..17) R.drawable.cloudy_day else R.drawable.cloudy_night
                    }
                    "4" -> R.drawable.cloudy
                    else -> R.drawable.sunny_icon
                }
            }
            weatherIcon?.setImageResource(weatherIconResId)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating weather UI: ${e.message}", e)
            findViewById<View>(R.id.weatherContainer)?.visibility = View.GONE
        }
    }

    private fun setupDismissButtonDrag() {
        dismissButton?.post {
            dismissButton?.let { button ->
                originalX = button.x
                originalY = button.y
            }
        }

        dismissButton?.setOnTouchListener { view, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    dX = view.x - event.rawX
                    dY = view.y - event.rawY
                    dragHintText?.animate()?.alpha(0f)?.duration = 200
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    view.animate()
                        .x(event.rawX + dX)
                        .y(event.rawY + dY)
                        .setDuration(0)
                        .start()
                    val distance = calculateDragDistance(view)
                    val opacity = (1 - (distance / DRAG_THRESHOLD)).coerceIn(0f, 1f)
                    view.alpha = opacity
                    true
                }
                MotionEvent.ACTION_UP -> {
                    if (isDraggedOut(view)) {
                        mediaPlayer?.let { player ->
                            AlarmHelper.dismissAlarm(
                                this,
                                alarmId,
                                player,
                                ::restoreVolume
                            ) {
                                cleanupAndFinish()
                            }
                        }
                    } else {
                        view.animate()
                            .x(originalX)
                            .y(originalY)
                            .alpha(1f)
                            .setDuration(300)
                            .setInterpolator(AccelerateDecelerateInterpolator())
                            .start()
                        dragHintText?.animate()?.alpha(1f)?.duration = 200
                    }
                    true
                }
                else -> false
            }
        }
    }

    private fun calculateDragDistance(view: View): Float {
        val deltaX = abs(view.x - originalX)
        val deltaY = abs(view.y - originalY)
        return sqrt(deltaX * deltaX + deltaY * deltaY)
    }

    private fun isDraggedOut(view: View): Boolean {
        return calculateDragDistance(view) >= DRAG_THRESHOLD
    }

    private fun cleanupAndFinish() {
        try {
            // 뷰 참조 해제
            alarmContainer = null
            dismissButton = null
            snoozeButton = null
            alarmTitle = null
            alarmBody = null
            currentTimeTextView = null
            currentDateTextView = null
            dragHintText = null
            weatherTemp = null
            weatherIcon = null
            weatherStatus = null
            weatherHumidity = null

            // 핸들러와 코루틴 정리
            handler?.removeCallbacksAndMessages(null)
            handler = null
            coroutineScope?.cancel()
            coroutineScope = null

            finish()
        } catch (e: Exception) {
            Log.e(TAG, "Error in cleanup", e)
            finish()
        }
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy() called")
        try {
            // MediaPlayer 정리는 BaseAlarmActivity에서 처리
            // 알람 노티피케이션 취소
            if (alarmId != -1) {
                NotificationHelper(this).cancelAlarmNotification(alarmId)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in onDestroy", e)
        } finally {
            // 뷰 참조 해제
            alarmContainer = null
            dismissButton = null
            snoozeButton = null
            alarmTitle = null
            alarmBody = null
            currentTimeTextView = null
            currentDateTextView = null
            dragHintText = null
            weatherTemp = null
            weatherIcon = null
            weatherStatus = null
            weatherHumidity = null

            // 핸들러와 코루틴 정리
            handler?.removeCallbacksAndMessages(null)
            handler = null
            coroutineScope?.cancel()
            coroutineScope = null

            super.onDestroy()
        }
    }

    override fun onBackPressed() {
        // 백버튼 무시
        Log.d(TAG, "onBackPressed() - ignored")
    }
}
