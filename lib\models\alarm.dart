import 'package:flutter/material.dart';
import 'package:flutter_voice_alarm/models/model.dart';
import 'package:intl/intl.dart';

class Alarm {
  final int id;
  final TimeOfDay time;
  final String title;
  final List<bool> repeatDays; // [<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Sat, Sun]
  final bool aiAlarm;
  final String? weatherType;
  final Map<String, dynamic>? weatherData;

  Alarm({
    required this.id,
    required this.time,
    required this.title,
    required this.repeatDays,
    this.aiAlarm = false,
    this.weatherType,
    this.weatherData,
  });

  Model toModel() {
    final now = DateTime.now();
    DateTime alarmTime = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );

    // 현재 시간보다 이전이면 다음 날로 설정
    if (alarmTime.isBefore(now) && !repeatDays.any((day) => day)) {
      alarmTime = alarmTime.add(const Duration(days: 1));
    }

    String when;
    if (repeatDays.any((day) => day)) {
      List<String> days = ['월', '화', '수', '목', '금', '토', '일'];
      when = repeatDays
          .asMap()
          .entries
          .where((entry) => entry.value)
          .map((entry) => days[entry.key])
          .join(', ');
    } else {
      when = DateFormat('MM월 dd일 HH:mm').format(alarmTime);
    }

    return Model(
      id: id,
      label: title,
      dateTime: DateFormat('HH:mm').format(alarmTime),
      check: true,
      when: when,
      milliseconds: alarmTime.millisecondsSinceEpoch,
      aiAlarm: aiAlarm,
      weatherType: weatherType,
      weatherData: weatherData,
      repeatDays: List<bool>.from(repeatDays),
    );
  }

  factory Alarm.fromModel(Model model) {
    final time = DateFormat('HH:mm').parse(model.dateTime);
    return Alarm(
      id: model.id,
      time: TimeOfDay(hour: time.hour, minute: time.minute),
      title: model.label,
      repeatDays: List<bool>.from(model.repeatDays), // 깊은 복사
      aiAlarm: model.aiAlarm,
      weatherType: model.weatherType,
      weatherData: model.weatherData,
    );
  }
}
