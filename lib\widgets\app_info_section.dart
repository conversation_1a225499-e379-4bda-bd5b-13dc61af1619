import 'package:flutter/material.dart';
import 'package:flutter_voice_alarm/utils/toast_utils.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppInfoSection extends StatefulWidget {
  const AppInfoSection({super.key});

  @override
  AppInfoSectionState createState() => AppInfoSectionState();
}

class AppInfoSectionState extends State<AppInfoSection> {
  int _clickCount = 0;
  bool _showFullAppInfo = false;

  Future<PackageInfo> _getAppInfo() async {
    return await PackageInfo.fromPlatform();
  }

  void _handleAppInfoClick() {
    _clickCount++;
    if (_clickCount == 10) {
      setState(() {
        _showFullAppInfo = true;
      });
      ToastUtils.showMessage('앱정보 전체보기 활성화');
    } else {
      ToastUtils.showMessage('$_clickCount번 클릭했습니다');
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<PackageInfo>(
      future: _getAppInfo(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator();
        } else if (snapshot.hasError) {
          return const ListTile(
            title: Text('앱 정보'),
            subtitle: Text('정보를 불러오는 중 오류가 발생했습니다.'),
          );
        }

        final appInfo = snapshot.data!;
        return ListTile(
          title: const Text('앱 정보'),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('버전: ${appInfo.version}'),
              if (_showFullAppInfo) ...[
                Text('패키지 이름: ${appInfo.packageName}'),
                Text('빌드 번호: ${appInfo.buildNumber}'),
              ],
            ],
          ),
          onTap: _handleAppInfoClick,
        );
      },
    );
  }
}
