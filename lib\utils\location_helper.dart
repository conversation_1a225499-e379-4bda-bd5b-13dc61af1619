import 'package:geolocator/geolocator.dart';
import 'package:logger/logger.dart';

final Logger _logger = Logger();

class LocationHelper {
  static Position? _cachedPosition;
  static DateTime? _lastUpdateTime;

  static Future<Position?> determinePosition() async {
    try {
      // 1. 캐시된 위치 확인
      if (_cachedPosition != null && _lastUpdateTime != null) {
        final difference = DateTime.now().difference(_lastUpdateTime!);
        if (difference.inMinutes < 10) {
          // 10분 이내의 캐시된 위치라면 사용
          _logger.i('Using cached position');
          return _cachedPosition;
        }
      }

      // 2. 위치 서비스 활성화 여부 확인
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _logger.i('Location services are disabled.');
        return null;
      }

      // 3. 권한 확인 및 요청
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _logger.i('Location permissions are denied');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _logger.i('Location permissions are permanently denied');
        return null;
      }

      _logger.i('Location permissions granted');

      // 4. 위치 정보 가져오기 시도
      try {
        _cachedPosition = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.medium, // 정확도를 medium으로 조정
          timeLimit: const Duration(seconds: 10), // 타임아웃 시간 증가
        );
        _lastUpdateTime = DateTime.now();
        _logger.i('Successfully got current position');
        return _cachedPosition;
      } catch (positionError) {
        _logger.w('Error getting current position: $positionError');

        // 5. 현재 위치 가져오기 실패시 마지막 알려진 위치 시도
        _logger.i('Trying to get last known position');
        _cachedPosition = await Geolocator.getLastKnownPosition();

        if (_cachedPosition != null) {
          _lastUpdateTime = DateTime.now();
          _logger.i('Using last known position');
          return _cachedPosition;
        }
      }

      // 6. 모든 시도 실패시
      _logger.w('Failed to get any position');
      return null;
    } catch (e) {
      _logger.e('Error in determinePosition: $e');
      return null;
    }
  }

  static Future<bool> isLocationPermissionGranted() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      bool granted = permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse;
      _logger.i('Location permission status: $granted');
      return granted;
    } catch (e) {
      _logger.e('Error checking location permission: $e');
      return false;
    }
  }

  // 캐시된 위치 초기화
  static void clearCache() {
    _cachedPosition = null;
    _lastUpdateTime = null;
    _logger.i('Location cache cleared');
  }
}
