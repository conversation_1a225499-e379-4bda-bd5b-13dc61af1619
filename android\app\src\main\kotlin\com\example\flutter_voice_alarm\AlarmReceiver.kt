package com.example.flutter_voice_alarm

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

class AlarmReceiver : BroadcastReceiver() {
    companion object {
        private const val TAG = "AlarmReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Received alarm intent: ${intent.action}")
        
        val alarmId = intent.getIntExtra("alarmId", -1)
        
        when (intent.action) {
            "DISMISS_ALARM" -> {
                handleDismissAlarm(context, alarmId)
            }
            "SNOOZE_ALARM" -> {
                handleSnoozeAlarm(context, alarmId)
            }
            else -> {
                // 알람 시작 (AI 알람 포함)
                val title = intent.getStringExtra("title") ?: "알람"
                val body = intent.getStringExtra("body") ?: ""
                val volume = intent.getFloatExtra("volume", 1.0f)
                val alarmSound = intent.getStringExtra("alarmSound")
                val isAIAlarm = intent.getBooleanExtra("isAIAlarm", false)

                Log.d(TAG, "Starting alarm: ID=$alarmId, AI=$isAIAlarm, Sound=$alarmSound")

                NotificationHelper(context).showAlarmNotification(
                    alarmId,
                    title,
                    body,
                    volume,
                    alarmSound,
                    isAIAlarm
                )
            }
        }
    }

    private fun handleDismissAlarm(context: Context, alarmId: Int) {
        Log.d(TAG, "Handling dismiss alarm: $alarmId")
        
        // 알람 액티비티 종료
        val dismissIntent = Intent(context, TopPopupAlarmActivity::class.java).apply {
            action = "DISMISS_ALARM"
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
            putExtra("alarmId", alarmId)
        }
        context.startActivity(dismissIntent)
        
        // 알림 취소
        NotificationHelper(context).cancelAlarmNotification(alarmId)
    }

    private fun handleSnoozeAlarm(context: Context, alarmId: Int) {
        Log.d(TAG, "Handling snooze alarm: $alarmId")
        
        // 알람 액티비티 종료 및 스누즈 처리
        val snoozeIntent = Intent(context, TopPopupAlarmActivity::class.java).apply {
            action = "SNOOZE_ALARM"
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
            putExtra("alarmId", alarmId)
        }
        context.startActivity(snoozeIntent)
        
        // 알림 취소
        NotificationHelper(context).cancelAlarmNotification(alarmId)
    }
}