package com.example.flutter_voice_alarm


import android.app.KeyguardManager
import android.content.Context
import android.media.AudioManager
import android.os.Build
import android.os.Bundle
import android.os.PowerManager
import android.util.DisplayMetrics
import android.util.Log
import android.view.Gravity
import android.view.WindowManager
import android.widget.TextView
import android.view.View
import com.google.android.material.button.MaterialButton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import java.lang.ref.WeakReference

class TopPopupAlarmActivity : BaseAlarmActivity() {
    companion object {
        private const val TAG = "TopPopupAlarmActivity"
    }

    private var dismissButton: MaterialButton? = null
    private var snoozeButton: MaterialButton? = null
    private var alarmTitle: TextView? = null

    private var alarmId: Int = -1
    private var coroutineScope: CoroutineScope? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 코루틴 스코프 초기화
        coroutineScope = CoroutineScope(Dispatchers.Main + Job())

        // 액션 확인
        when (intent.action) {
            "DISMISS_ALARM" -> {
                handleDismissAlarm()
                return
            }
            "SNOOZE_ALARM" -> {
                handleSnoozeAlarm()
                return
            }
        }

        // Flutter 초기화 (알람 액티비티에서는 필요하지 않음)
        // FlutterLoader().ensureInitializationComplete(applicationContext, null)
        setupPopupWindow()
        setContentView(R.layout.activity_top_popup_alarm)

        try {
            initAudio()
            wakeUpScreenForPopup() // 화면 깨우기 추가
            initializeViews()
            initMediaPlayer()
            setupButtonListeners()
        } catch (e: Exception) {
            Log.e(TAG, "Error in onCreate", e)
            cleanupAndFinish()
        }
    }

    private fun setupPopupWindow() {
    // 상태바 높이 구하기
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        val statusBarHeight = if (resourceId > 0) {
            resources.getDimensionPixelSize(resourceId)
        } else {
            0
        }

        // 화면 너비 구하기
        val displayMetrics = DisplayMetrics()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            display?.getRealMetrics(displayMetrics)
        } else {
            @Suppress("DEPRECATION")
            windowManager.defaultDisplay.getMetrics(displayMetrics)
        }
        val screenWidth = displayMetrics.widthPixels

        // 윈도우 레이아웃 파라미터 설정
        val params = window.attributes.apply {
            width = (screenWidth * 0.95).toInt() // 화면 너비의 95% 사용
            height = WindowManager.LayoutParams.WRAP_CONTENT
            gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL // 상단 중앙 정렬
            y = statusBarHeight + resources.getDimensionPixelSize(R.dimen.popup_margin_top) // 상태바 높이 + 추가 마진
            // 터치 이벤트 처리를 위한 플래그 추가
            flags = flags or WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
            // 배경을 투명하게 설정
            format = android.graphics.PixelFormat.TRANSLUCENT
        }
        
        window.attributes = params

        // 팝업 윈도우 플래그 설정
        window.addFlags(
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
            WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON or
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
            WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL // 팝업 외부 터치 허용
        )

        // 배경을 투명하게 설정
        window.setBackgroundDrawableResource(android.R.color.transparent)

        // API 27 이상에서는 키가드 해제
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            val keyguardManager = getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
            keyguardManager.requestDismissKeyguard(this, null)
        }
    }

    private fun wakeUpScreenForPopup() {
        wakeUpScreen() // 부모 클래스의 기본 wakeUpScreen 호출

        try {
            // 추가적인 팝업 전용 화면 깨우기 로직
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            val wakeLock = powerManager.newWakeLock(
                PowerManager.SCREEN_BRIGHT_WAKE_LOCK or PowerManager.ACQUIRE_CAUSES_WAKEUP,
                "VoiceAlarm:PopupWakeUpScreen"
            )
            wakeLock.acquire(10 * 60 * 1000L) // 10분간 유지

            val keyguardManager = getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
            if (keyguardManager.isKeyguardLocked) {
                Log.d(TAG, "Device is locked, showing popup alarm on lock screen")
            }

            // 무음 모드 강제 해제 (알람용)
            forceUnmuteForAlarm()

        } catch (e: Exception) {
            Log.e(TAG, "Error in popup wakeUpScreen", e)
        }
    }

    private fun forceUnmuteForAlarm() {
        try {
            val audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
            val currentRingerMode = audioManager.ringerMode

            Log.d(TAG, "Current ringer mode: $currentRingerMode")

            when (currentRingerMode) {
                AudioManager.RINGER_MODE_SILENT -> {
                    Log.d(TAG, "Device is in silent mode, forcing alarm volume")
                    // 무음 모드에서도 알람 볼륨 강제 설정
                    val maxAlarmVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_ALARM)
                    audioManager.setStreamVolume(
                        AudioManager.STREAM_ALARM,
                        (maxAlarmVolume * 0.8).toInt(), // 80% 볼륨
                        AudioManager.FLAG_ALLOW_RINGER_MODES
                    )
                }
                AudioManager.RINGER_MODE_VIBRATE -> {
                    Log.d(TAG, "Device is in vibrate mode, forcing alarm volume")
                    // 진동 모드에서도 알람 볼륨 강제 설정
                    val maxAlarmVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_ALARM)
                    audioManager.setStreamVolume(
                        AudioManager.STREAM_ALARM,
                        (maxAlarmVolume * 0.8).toInt(), // 80% 볼륨
                        AudioManager.FLAG_ALLOW_RINGER_MODES
                    )
                }
                AudioManager.RINGER_MODE_NORMAL -> {
                    Log.d(TAG, "Device is in normal mode")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error forcing unmute for alarm", e)
        }
    }

    private fun initializeViews() {
        try {
            // 뷰 초기화
            dismissButton = findViewById<MaterialButton>(R.id.dismissButton)
            snoozeButton = findViewById<MaterialButton>(R.id.snoozeButton)
            alarmTitle = findViewById<TextView>(R.id.alarmTitle)

            // 인텐트로 전달된 값 설정
            alarmId = intent.getIntExtra("alarmId", -1)
            alarmTitle?.text = intent.getStringExtra("title") ?: "알람"
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing views", e)
            cleanupAndFinish()
        }
    }

    private fun initMediaPlayer() {
        val volume = intent.getFloatExtra("volume", 1.0f)
        val alarmSound = intent.getStringExtra("alarmSound") ?: "lifes_good_alarm"
        Log.d(TAG, "Alarm sound: $alarmSound, volume: $volume")

        initializeMediaPlayerCommon(volume, alarmSound) { sound ->
            if (sound.startsWith("voice1/") || sound.startsWith("voice2/")) {
                "assets/sounds/$sound"
            } else {
                "assets/sounds/voice2/$sound"
            }
        }
    }

    private fun setupButtonListeners() {
        snoozeButton?.setOnClickListener {
            snoozeButton?.isEnabled = false
            mediaPlayer?.let { player ->
                AlarmHelper.snoozeAlarm(
                    this,
                    alarmId,
                    player,
                    ::restoreVolume
                ) {
                    cleanupAndFinish()
                }
            }
        }

        dismissButton?.setOnClickListener {
            dismissButton?.isEnabled = false
            mediaPlayer?.let { player ->
                AlarmHelper.dismissAlarm(
                    this,
                    alarmId,
                    player,
                    ::restoreVolume
                ) {
                    cleanupAndFinish()
                }
            }
        }
    }

    private fun handleDismissAlarm() {
        mediaPlayer?.let { player ->
            AlarmHelper.dismissAlarm(
                this,
                alarmId,
                player,
                ::restoreVolume
            ) {
                cleanupAndFinish()
            }
        } ?: cleanupAndFinish()
    }

    private fun handleSnoozeAlarm() {
        mediaPlayer?.let { player ->
            AlarmHelper.snoozeAlarm(
                this,
                alarmId,
                player,
                ::restoreVolume
            ) {
                cleanupAndFinish()
            }
        } ?: cleanupAndFinish()
    }

    private fun cleanupAndFinish() {
        try {
            // 뷰 참조 해제
            dismissButton = null
            snoozeButton = null
            alarmTitle = null

            // 코루틴 스코프 정리
            coroutineScope?.cancel()
            coroutineScope = null

            finish()
        } catch (e: Exception) {
            Log.e(TAG, "Error in cleanup", e)
            finish()
        }
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy() called")
        try {
            // MediaPlayer 정리는 BaseAlarmActivity에서 처리
            // 알람 노티피케이션 취소
            if (alarmId != -1) {
                NotificationHelper(this).cancelAlarmNotification(alarmId)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in onDestroy", e)
        } finally {
            // 뷰 참조 해제
            dismissButton = null
            snoozeButton = null
            alarmTitle = null

            // 코루틴 스코프 정리
            coroutineScope?.cancel()
            coroutineScope = null

            super.onDestroy()
        }
    }

    override fun onBackPressed() {
        // 백버튼 무시
        Log.d(TAG, "onBackPressed() - ignored in popup")
    }
}